"""
Alternative implementation using JSON Schema approach.
This shows how you could use Pydantic's JSON Schema generation instead of type hints.
"""

from typing import Sequence
from pydantic import Field

from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode, type_tree


class SuggestNodesFieldsJsonSchemaUseCase:
    """Alternative implementation using JSON Schema approach."""

    def execute(self, nodes: Sequence[TreeNode]) -> dict[str, str]:
        """
        Execute the use case using JSON Schema approach.
        
        Args:
            nodes: Sequence of tree nodes to analyze
            
        Returns:
            Dictionary with field names as keys and JSON Schema type strings as values
        """
        data_types = set.union(*(type_tree.get_node_data_types(node) for node in nodes))

        # Get JSON schemas for each data type
        schemas = [
            data_type.to_domain_model().model_json_schema() 
            for data_type in data_types
        ]

        # Find intersection of properties across all schemas
        if not schemas:
            return {}
            
        # Get common properties
        common_properties = set.intersection(*(set(schema.get("properties", {}).keys()) for schema in schemas))
        
        # Build the result using the first schema as reference for types
        result = {}
        first_schema = schemas[0]
        
        for prop_name in common_properties:
            if prop_name in first_schema.get("properties", {}):
                prop_schema = first_schema["properties"][prop_name]
                result[prop_name] = self._json_schema_to_type_string(prop_schema)
                
                # Handle nested objects
                if prop_schema.get("type") == "object" and "properties" in prop_schema:
                    nested_fields = self._expand_nested_schema(prop_schema, prop_name)
                    result.update(nested_fields)
                    # Remove the parent object field since we're expanding it
                    del result[prop_name]
        
        return result
    
    def _json_schema_to_type_string(self, schema: dict) -> str:
        """
        Convert a JSON Schema property to a type string.
        
        Args:
            schema: JSON Schema property definition
            
        Returns:
            String representation of the type
        """
        schema_type = schema.get("type")
        
        if schema_type == "string":
            return "string"
        elif schema_type == "integer":
            return "integer"
        elif schema_type == "number":
            return "number"
        elif schema_type == "boolean":
            return "boolean"
        elif schema_type == "array":
            items_type = self._json_schema_to_type_string(schema.get("items", {}))
            return f"array<{items_type}>"
        elif schema_type == "object":
            return "object"
        elif isinstance(schema_type, list):
            # Handle union types like ["string", "null"]
            non_null_types = [t for t in schema_type if t != "null"]
            if len(non_null_types) == 1:
                base_type = self._json_schema_to_type_string({"type": non_null_types[0]})
                if "null" in schema_type:
                    return f"{base_type} | null"
                return base_type
            else:
                type_strings = [self._json_schema_to_type_string({"type": t}) for t in non_null_types]
                result = " | ".join(type_strings)
                if "null" in schema_type:
                    result += " | null"
                return result
        else:
            # Handle anyOf, oneOf, etc.
            if "anyOf" in schema:
                return self._handle_any_of(schema["anyOf"])
            elif "oneOf" in schema:
                return self._handle_any_of(schema["oneOf"])
            else:
                return "unknown"
    
    def _handle_any_of(self, any_of_schemas: list) -> str:
        """Handle anyOf/oneOf schemas."""
        type_strings = []
        has_null = False
        
        for sub_schema in any_of_schemas:
            if sub_schema.get("type") == "null":
                has_null = True
            else:
                type_strings.append(self._json_schema_to_type_string(sub_schema))
        
        result = " | ".join(type_strings)
        if has_null:
            result += " | null"
        return result
    
    def _expand_nested_schema(self, schema: dict, prefix: str) -> dict[str, str]:
        """
        Recursively expand nested object schemas into dot notation.
        
        Args:
            schema: JSON Schema object definition
            prefix: Current dot notation prefix
            
        Returns:
            Dictionary with expanded field names and their types
        """
        result = {}
        properties = schema.get("properties", {})
        
        for prop_name, prop_schema in properties.items():
            full_name = f"{prefix}.{prop_name}"
            
            if prop_schema.get("type") == "object" and "properties" in prop_schema:
                # Recursively expand nested objects
                nested = self._expand_nested_schema(prop_schema, full_name)
                result.update(nested)
            else:
                # Add the field with its type
                result[full_name] = self._json_schema_to_type_string(prop_schema)
        
        return result


# Example usage comparison
def compare_approaches():
    """Compare the two approaches side by side."""
    from services.data_service.application.use_cases.type_tree.suggest_nodes_fields_use_case import (
        SuggestNodesFieldsUseCase,
        SuggestNodesFieldsUseCaseInputBoundary,
    )
    
    # Test nodes
    food_node = TreeNode.from_path("doc.event.nutrition.food")
    nodes = [food_node]
    
    # Type hints approach
    type_hints_use_case = SuggestNodesFieldsUseCase()
    type_hints_input = SuggestNodesFieldsUseCaseInputBoundary(nodes=nodes)
    type_hints_result = type_hints_use_case.execute(type_hints_input)
    
    # JSON Schema approach
    json_schema_use_case = SuggestNodesFieldsJsonSchemaUseCase()
    json_schema_result = json_schema_use_case.execute(nodes)
    
    print("=== Type Hints Approach ===")
    for field, field_type in sorted(type_hints_result.fields.items())[:10]:
        print(f"  {field}: {field_type}")
    
    print("\n=== JSON Schema Approach ===")
    for field, field_type in sorted(json_schema_result.items())[:10]:
        print(f"  {field}: {field_type}")


if __name__ == "__main__":
    compare_approaches()
