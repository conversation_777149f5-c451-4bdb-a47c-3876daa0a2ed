from enum import Enum
from typing import Annotated, Sequence, TypeVar
from uuid import UUID

from pydantic import UUID4, AfterValidator, AwareDatetime, Field, PlainSerializer

from services.base.domain.validations.validate_html import validate_html
from services.base.domain.validations.validate_node_str import validate_node_str
from services.base.domain.validations.validate_non_empty_length import validate_non_empty_length
from services.base.domain.validations.validate_unique_sequence import validate_unique_sequence

EnumType = TypeVar("EnumType", bound=Enum)
UniqueSequenceType = TypeVar("UniqueSequenceType", bound=Sequence)

SerializableAwareDatetime = Annotated[
    AwareDatetime, PlainSerializer(lambda d: d.isoformat(timespec="milliseconds"), when_used="unless-none")
]
NonEmptyStr = Annotated[str, AfterValidator(lambda s: validate_non_empty_length(s))]
UniqueSequence = Annotated[UniqueSequenceType, AfterValidator(lambda s: validate_unique_sequence(s))]
UniqueSequenceStr = UniqueSequence[Sequence[NonEmptyStr]]
UniqueSequenceUUID = UniqueSequence[Sequence[UUID]]
SerializableUUID = Annotated[UUID4, PlainSerializer(lambda uuid: str(uuid), when_used="unless-none", return_type=str)]
NonEmptyBytes = Annotated[bytes, AfterValidator(lambda b: validate_non_empty_length(b))]
RoundedFloat = Annotated[float, AfterValidator(lambda f: round(f, 3) if f else f)]
Rounded6Float = Annotated[float, AfterValidator(lambda f: round(f, 6) if f else f)]
# Used for latitude,longitude measurements
CoordinatesFloat = Annotated[float, AfterValidator(lambda f: round(f, 5) if f else f)]
ASSET_ID_REGEX_PATTERN = r"^[A-Za-z0-9._-]+$"
AssetId = Annotated[NonEmptyStr, Field(pattern=ASSET_ID_REGEX_PATTERN)]
NodeStr = Annotated[str, AfterValidator(lambda s: validate_node_str(s))]
HTML = Annotated[str, AfterValidator(lambda s: validate_html(html_as_str=s))]
