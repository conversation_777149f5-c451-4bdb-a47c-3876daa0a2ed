from typing import Sequence, get_origin, get_args
from types import UnionType, NoneType

from pydantic import Field

from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode, type_tree
from services.data_service.type_resolver import TypeResolver


class SuggestNodesFieldsUseCaseInputBoundary(BaseDataModel):
    nodes: Sequence[TreeNode]
    #max_depth: int = Field(default=0, ge=0)

class FieldInfo(BaseDataModel):
    type: str
    enumref: str
    is_nullable: bool


class SuggestNodesFieldsUseCaseOutputBoundary(BaseDataModel):
    nodes: Sequence[TreeNode]
    fields: dict[str, str]
    enums: dict[str, list[str | int]]


class SuggestNodesFieldsUseCase:

    def execute(
        self, input_boundary: SuggestNodesFieldsUseCaseInputBoundary
    ) -> SuggestNodesFieldsUseCaseOutputBoundary:
        data_types = set.union(*(type_tree.get_node_data_types(node) for node in input_boundary.nodes))

        type_mappings = [
            FieldValidator.get_type_properties(data_type.to_domain_model()) for data_type in data_types # TODO: should use APIOutput
        ]

        common_keys = set.intersection(*(set(m.keys()) for m in type_mappings))
        combined_type_mapping = {k: type_mappings[0][k] for k in common_keys}

        # Expand nested fields hierarchically
        expanded_fields = self._expand_fields_hierarchically(combined_type_mapping)

        # Convert types to string representations for JSON serialization
        string_fields = {field_name: self._type_to_string(field_type) for field_name, field_type in expanded_fields.items()}

        return SuggestNodesFieldsUseCaseOutputBoundary(fields=string_fields, nodes=input_boundary.nodes)

    def _expand_fields_hierarchically(self, type_mapping: dict[str, type], prefix: str = "") -> dict[str, type]:
        """
        Recursively expand nested BaseDataModel fields into dot notation.

        Args:
            type_mapping: Dictionary of field names to their types
            prefix: Current dot notation prefix for nested fields

        Returns:
            Dictionary with expanded dot notation field names and their primitive types
        """
        expanded = {}

        for field_name, field_type in type_mapping.items():
            full_field_name = f"{prefix}.{field_name}" if prefix else field_name

            # Handle Union types (e.g., Optional fields like int | None)
            resolved_type = self._resolve_union_type(field_type)

            # Check if this is a BaseDataModel that should be expanded
            if self._is_expandable_model(resolved_type):
                # Get the nested model's fields and expand them recursively
                nested_fields = FieldValidator.get_type_properties(resolved_type)
                nested_expanded = self._expand_fields_hierarchically(nested_fields, full_field_name)
                expanded.update(nested_expanded)
            else:
                # This is a primitive type, add it directly
                expanded[full_field_name] = field_type

        return expanded

    def _resolve_union_type(self, field_type: type) -> type:
        """
        Resolve Union types (like Optional) to get the actual data type.

        Args:
            field_type: The type to resolve

        Returns:
            The resolved non-None type, or the original type if not a Union
        """
        origin = get_origin(field_type)
        if origin in (UnionType, type(int | None)):  # Handle Union types
            args = get_args(field_type)
            # Filter out NoneType to get the actual type
            non_none_types = [arg for arg in args if arg is not NoneType]
            if len(non_none_types) == 1:
                return non_none_types[0]

        return field_type

    def _is_expandable_model(self, field_type: type) -> bool:
        """
        Check if a type is a BaseDataModel that should be expanded into nested fields.

        Args:
            field_type: The type to check

        Returns:
            True if the type should be expanded, False otherwise
        """
        try:
            return (
                hasattr(field_type, '__bases__') and
                issubclass(field_type, BaseDataModel) and
                hasattr(field_type, 'model_fields')
            )
        except (TypeError, AttributeError):
            return False

    def _type_to_string(self, field_type: type) -> str:
        """
        Convert a Python type to a string representation suitable for JSON serialization.

        Args:
            field_type: The type to convert

        Returns:
            String representation of the type
        """
        # Handle Union types (e.g., int | None, Optional[str])
        origin = get_origin(field_type)
        if origin in (UnionType, type(int | None)):
            args = get_args(field_type)
            # Filter out NoneType and create a readable representation
            non_none_types = [arg for arg in args if arg is not NoneType]
            if len(non_none_types) == 1:
                base_type = self._get_simple_type_name(non_none_types[0])
                return f"{base_type} | null"
            else:
                type_names = [self._get_simple_type_name(arg) for arg in non_none_types]
                return " | ".join(type_names) + " | null"

        # Handle simple types
        return self._get_simple_type_name(field_type)

    def _get_simple_type_name(self, field_type: type) -> str:
        """
        Get a simple, readable name for a type.

        Args:
            field_type: The type to get the name for

        Returns:
            Simple string name for the type
        """
        # Handle built-in types
        if field_type == str:
            return "string"
        elif field_type == int:
            return "integer"
        elif field_type == float:
            return "number"
        elif field_type == bool:
            return "boolean"
        elif hasattr(field_type, '__name__'):
            # For custom types like Rounded6Float, enums, etc.
            return field_type.__name__
        else:
            # Fallback to string representation
            return str(field_type)