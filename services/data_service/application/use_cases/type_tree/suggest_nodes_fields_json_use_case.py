from typing import Sequence

from pydantic import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode, type_tree


class SuggestNodesFieldsJsonUseCaseInputBoundary(BaseDataModel):
    nodes: Sequence[TreeNode]
    #max_depth: int = Field(default=0, ge=0)


class SuggestNodesFieldsJsonUseCaseOutputBoundary(BaseDataModel):
    fields: dict[str, str]


class SuggestNodesFieldsJsonUseCase:

    def execute(
        self, input_boundary: SuggestNodesFieldsJsonUseCaseInputBoundary
    ) -> SuggestNodesFieldsJsonUseCaseOutputBoundary:
        data_types = set.union(*(type_tree.get_node_data_types(node) for node in input_boundary.nodes))

        # Get JSON schemas for each data type
        schemas = [
            data_type.to_domain_model().model_json_schema()
            for data_type in data_types
        ]

        # Find intersection of properties across all schemas
        if not schemas:
            return SuggestNodesFieldsJsonUseCaseOutputBoundary(fields={})

        # Get common properties
        common_properties = set.intersection(*(set(schema.get("properties", {}).keys()) for schema in schemas))

        # Build the result using the first schema as reference for types
        result = {}
        first_schema = schemas[0]

        for prop_name in common_properties:
            if prop_name in first_schema.get("properties", {}):
                prop_schema = first_schema["properties"][prop_name]

                # Handle nested objects by expanding them hierarchically
                if prop_schema.get("type") == "object" and "properties" in prop_schema:
                    nested_fields = self._expand_nested_schema(prop_schema, prop_name)
                    result.update(nested_fields)
                else:
                    # This is a primitive type, add it directly
                    result[prop_name] = self._json_schema_to_type_string(prop_schema)

        return SuggestNodesFieldsJsonUseCaseOutputBoundary(fields=result)

    def _expand_nested_schema(self, schema: dict, prefix: str = "") -> dict[str, str]:
        """
        Recursively expand nested object schemas into dot notation.

        Args:
            schema: JSON schema object to expand
            prefix: Current dot notation prefix for nested fields

        Returns:
            Dictionary with expanded dot notation field names and their type strings
        """
        expanded = {}

        properties = schema.get("properties", {})
        for field_name, field_schema in properties.items():
            full_field_name = f"{prefix}.{field_name}" if prefix else field_name

            # Check if this is a nested object that should be expanded
            if field_schema.get("type") == "object" and "properties" in field_schema:
                # Recursively expand nested objects
                nested_expanded = self._expand_nested_schema(field_schema, full_field_name)
                expanded.update(nested_expanded)
            else:
                # This is a primitive type, add it directly
                expanded[full_field_name] = self._json_schema_to_type_string(field_schema)

        return expanded

    def _json_schema_to_type_string(self, schema: dict) -> str:
        """
        Convert a JSON schema property to a string representation.

        Args:
            schema: JSON schema property definition

        Returns:
            String representation of the type
        """
        # Handle anyOf (Union types like Optional fields)
        if "anyOf" in schema:
            types = []
            has_null = False

            for any_of_schema in schema["anyOf"]:
                if any_of_schema.get("type") == "null":
                    has_null = True
                else:
                    types.append(self._get_simple_json_type_name(any_of_schema))

            if len(types) == 1 and has_null:
                return f"{types[0]} | null"
            elif types:
                result = " | ".join(types)
                return f"{result} | null" if has_null else result
            else:
                return "null"

        # Handle simple types
        return self._get_simple_json_type_name(schema)

    def _get_simple_json_type_name(self, schema: dict) -> str:
        """
        Get a simple, readable name for a JSON schema type.

        Args:
            schema: JSON schema type definition

        Returns:
            Simple string name for the type
        """
        schema_type = schema.get("type")

        if schema_type == "string":
            return "string"
        elif schema_type == "integer":
            return "integer"
        elif schema_type == "number":
            return "number"
        elif schema_type == "boolean":
            return "boolean"
        elif schema_type == "array":
            return "array"
        elif schema_type == "object":
            return "object"
        elif schema_type == "null":
            return "null"
        else:
            # Handle custom types or fallback
            title = schema.get("title")
            if title:
                return title
            return str(schema_type) if schema_type else "unknown"