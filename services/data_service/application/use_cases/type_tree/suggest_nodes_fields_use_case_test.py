import pytest
from types import UnionType
from typing import get_origin

from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.type_tree.type_tree import TreeNode
from services.data_service.application.use_cases.type_tree.suggest_nodes_fields_use_case import (
    SuggestNodesFieldsUseCase,
    SuggestNodesFieldsUseCaseInputBoundary,
)


class TestSuggestNodesFieldsUseCase:

    @pytest.mark.parametrize("node_path,expected", [
        pytest.param(
            "doc.event.exercise.cardio",
            {"name": "string", "rating": "integer | null", "category": "string", "distance": "number | null", "elevation": "number | null"},
            id="cardio"
        ),
    ])
    def test_single_node_contains_expected_fields(self, node_path, expected):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        node = TreeNode.from_path(node_path)
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        assert expected.items() <= result.fields.items()
        assert all(item in expected.items() for item in result.fields.items())

    def test_empty_nodes_list_raises_error(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[])
        
        # Act & Assert
        with pytest.raises(TypeError):  # set.union with empty sequence should raise TypeError
            use_case.execute(input_boundary)
            
    def test_resolve_union_type_handles_optional_types(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()

        # Act & Assert
        # Test with int | None
        resolved = use_case._resolve_union_type(int | None)
        assert resolved == int

        # Test with non-union type
        resolved = use_case._resolve_union_type(str)
        assert resolved == str

    def test_type_to_string_converts_types_correctly(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()

        # Act & Assert
        assert use_case._type_to_string(str) == "string"
        assert use_case._type_to_string(int) == "integer"
        assert use_case._type_to_string(float) == "number"
        assert use_case._type_to_string(bool) == "boolean"
        assert use_case._type_to_string(int | None) == "integer | null"
        assert use_case._type_to_string(str | None) == "string | null"

        # Test with custom types
        from services.base.domain.annotated_types import Rounded6Float
        assert use_case._type_to_string(Rounded6Float) == "Rounded6Float"
        assert use_case._type_to_string(Rounded6Float | None) == "Rounded6Float | null"
        
    def test_is_expandable_model_identifies_base_data_models(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        
        # Import a known BaseDataModel for testing
        from services.base.domain.schemas.events.nutrition.nutrients import Nutrients
        from services.base.domain.schemas.shared import BaseDataModel
        
        # Act & Assert
        assert use_case._is_expandable_model(Nutrients) == True
        assert use_case._is_expandable_model(BaseDataModel) == True
        assert use_case._is_expandable_model(str) == False
        assert use_case._is_expandable_model(int) == False
        assert use_case._is_expandable_model(None) == False
        
    def test_expand_fields_hierarchically_with_simple_types(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        simple_mapping = {
            "name": str,
            "age": int,
            "height": float | None
        }
        
        # Act
        result = use_case._expand_fields_hierarchically(simple_mapping)
        
        # Assert
        assert result == simple_mapping  # Should be unchanged for primitive types
        
    def test_expand_fields_hierarchically_with_nested_prefix(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        simple_mapping = {
            "protein": float | None,
            "fat": float | None
        }
        
        # Act
        result = use_case._expand_fields_hierarchically(simple_mapping, prefix="nutrients")
        
        # Assert
        expected = {
            "nutrients.protein": float | None,
            "nutrients.fat": float | None
        }
        assert result == expected
