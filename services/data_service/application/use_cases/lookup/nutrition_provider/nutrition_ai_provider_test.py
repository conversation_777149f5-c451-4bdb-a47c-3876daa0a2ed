import os

import pytest

from services.base.domain.enums.data_types import DataType
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_ai_provider import (
    NutritionAIProvider,
)


class TestNutritionAIProviderIntegration:
    @pytest.mark.integration
    @pytest.mark.parametrize(
        "query,expected_type",
        [
            ("grilled chicken breast", DataType.Food),
            ("apple", DataType.Food),
            ("coca cola", DataType.Drink),
            ("water", DataType.Drink),
            ("multivitamin", DataType.Supplement),
            ("green tea", DataType.Drink),
        ],
    )
    async def test_lookup_categorizes_items_correctly(
        self,
        nutrition_ai_provider: NutritionAIProvider,
        query: str,
        expected_type: DataType,
    ):
        result = await nutrition_ai_provider.lookup(name=query)

        assert result is not None
        assert result.documents is not None
        assert len(result.documents) == 1, f"Expected exactly 1 result for '{query}'"

        first_result = result.documents[0]
        assert (
            first_result.type == expected_type
        ), f"Expected {expected_type.value} for '{query}', got {first_result.type.value}"

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "query",
        [
            "grilled salmon with lemon",
            "chocolate chip cookies",
            "fresh orange juice",
            "vitamin c tablets",
            "caesar salad",
            "green smoothie",
        ],
    )
    async def test_lookup_returns_valid_nutrition_data(
        self,
        nutrition_ai_provider: NutritionAIProvider,
        query: str,
    ):
        result = await nutrition_ai_provider.lookup(name=query)

        assert result is not None
        assert result.documents is not None
        assert len(result.documents) == 1

        first_result = result.documents[0]
        assert first_result.type in [DataType.Food, DataType.Drink, DataType.Supplement]
        assert first_result.name is not None
        assert len(first_result.name) > 0
        assert first_result.consumed_amount is not None
        assert first_result.consumed_amount > 0
        assert first_result.consumed_type is not None
        assert first_result.nutrients is not None
        assert first_result.timestamp is not None

        nutrients = first_result.nutrients

        has_nutrients = any(
            [
                nutrients.carbohydrates is not None,
                nutrients.protein is not None,
                nutrients.fat is not None,
                nutrients.fiber is not None,
                nutrients.sugar is not None,
            ]
        )
        assert has_nutrients, f"Expected at least some nutrient data for '{query}'"

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "image_filename,expected_type",
        [
            ("pizza_food.jpg", DataType.Food),
            ("water_drink.jpg", DataType.Drink),
            ("vitamin_supplement.jpg", DataType.Supplement),
        ],
    )
    async def test_lookup_image_categorizes_correctly(
        self,
        nutrition_ai_provider: NutritionAIProvider,
        image_filename: str,
        expected_type: DataType,
    ):
        image_path = os.path.join(os.path.dirname(__file__), "..", "..", "ai", "images", image_filename)

        with open(image_path, "rb") as image_file:
            image_data = image_file.read()

        result = await nutrition_ai_provider.lookup_image(image=image_data)

        assert result is not None
        assert result.documents is not None
        assert len(result.documents) == 1

        first_result = result.documents[0]
        assert (
            first_result.type == expected_type
        ), f"Expected {expected_type.value} for '{image_filename}', got {first_result.type.value}"

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "image_filename,expected_content_keywords",
        [
            ("pizza_food.jpg", ["pizza"]),
            ("water_drink.jpg", ["water"]),
            ("vitamin_supplement.jpg", ["vitamin", "supplement"]),
        ],
    )
    async def test_lookup_image_returns_relevant_content(
        self,
        nutrition_ai_provider: NutritionAIProvider,
        image_filename: str,
        expected_content_keywords: list[str],
    ):
        image_path = os.path.join(os.path.dirname(__file__), "..", "..", "ai", "images", image_filename)

        with open(image_path, "rb") as image_file:
            image_data = image_file.read()

        result = await nutrition_ai_provider.lookup_image(image=image_data)

        assert result is not None
        assert result.documents is not None
        assert len(result.documents) == 1

        first_result = result.documents[0]
        assert first_result.name is not None

        name_lower = first_result.name.lower()
        found_keyword = any(keyword.lower() in name_lower for keyword in expected_content_keywords)
        assert found_keyword, f"Expected one of {expected_content_keywords} in name '{first_result.name}'"

    @pytest.mark.integration
    async def test_lookup_image_with_name_hint(
        self,
        nutrition_ai_provider: NutritionAIProvider,
    ):
        image_path = os.path.join(os.path.dirname(__file__), "..", "..", "ai", "images", "pizza_food.jpg")

        with open(image_path, "rb") as image_file:
            image_data = image_file.read()

        # Test with name hint
        result_with_hint = await nutrition_ai_provider.lookup_image(image=image_data, name="margherita pizza")

        assert result_with_hint is not None
        assert result_with_hint.documents is not None
        assert len(result_with_hint.documents) == 1

        first_result = result_with_hint.documents[0]
        assert first_result.type == DataType.Food
        assert first_result.name is not None
        assert "pizza" in first_result.name.lower()

    @pytest.mark.integration
    async def test_lookup_upc_not_implemented(
        self,
        nutrition_ai_provider: NutritionAIProvider,
    ):
        with pytest.raises(NotImplementedError, match="UPC lookup is not supported for AI provider"):
            await nutrition_ai_provider.lookup_upc(upc="123456789012")

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "food_query,expected",
        [
            ("grilled chicken breast", ["protein", "consumed_amount", "consumed_type"]),
            ("banana", ["carbohydrates", "consumed_amount", "consumed_type"]),
        ],
    )
    async def test_lookup_food_has_appropriate_fields(
        self,
        nutrition_ai_provider: NutritionAIProvider,
        food_query: str,
        expected_fields: list[str],
    ):
        result = await nutrition_ai_provider.lookup(name=food_query)

        assert result.documents is not None
        assert len(result.documents) == 1

        first_result = result.documents[0]
        assert first_result.type == DataType.Food

        assert first_result.nutrients is not None
        for field in expected_fields:
            if field == "protein":
                assert first_result.nutrients.protein is not None, f"Expected {field} to be present for '{food_query}'"
            elif field == "carbohydrates":
                assert (
                    first_result.nutrients.carbohydrates is not None
                ), f"Expected {field} to be present for '{food_query}'"
            elif field == "fat":
                assert first_result.nutrients.fat is not None, f"Expected {field} to be present for '{food_query}'"
            elif field == "consumed_amount":
                assert first_result.consumed_amount is not None, f"Expected {field} to be present for '{food_query}'"
                assert first_result.consumed_amount > 0, f"Expected {field} to be positive for '{food_query}'"
            elif field == "consumed_type":
                assert first_result.consumed_type is not None, f"Expected {field} to be present for '{food_query}'"

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "drink_query",
        [
            "orange juice",
            "coca cola",
            "green tea",
        ],
    )
    async def test_lookup_drink_has_appropriate_serving_info(
        self,
        nutrition_ai_provider: NutritionAIProvider,
        drink_query: str,
    ):
        result = await nutrition_ai_provider.lookup(name=drink_query)

        assert result.documents is not None
        assert len(result.documents) == 1

        first_result = result.documents[0]
        assert first_result.type == DataType.Drink

        # Drinks should have appropriate consumed type and amount
        assert first_result.consumed_amount is not None
        assert first_result.consumed_amount > 0
        assert first_result.consumed_type is not None

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "complex_query,expected_type",
        [
            ("homemade chicken caesar salad with croutons and parmesan", DataType.Food),
            ("fresh squeezed orange juice with pulp", DataType.Drink),
            ("omega-3 fish oil softgel capsules 1000mg", DataType.Supplement),
            ("grilled salmon fillet with herbs and lemon", DataType.Food),
            ("green smoothie with spinach, banana, and protein powder", DataType.Drink),
        ],
    )
    async def test_lookup_handles_complex_descriptions(
        self,
        nutrition_ai_provider: NutritionAIProvider,
        complex_query: str,
        expected_type: DataType,
    ):
        result = await nutrition_ai_provider.lookup(name=complex_query)

        assert result is not None
        assert result.documents is not None
        assert len(result.documents) == 1

        first_result = result.documents[0]
        assert (
            first_result.type == expected_type
        ), f"Expected {expected_type.value} for '{complex_query}', got {first_result.type.value}"
        assert first_result.name is not None
        assert len(first_result.name) > 0
        assert first_result.nutrients is not None
